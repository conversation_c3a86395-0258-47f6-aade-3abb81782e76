#!/usr/bin/env python3
"""
Script to extract unique values from "Etap w rekrutacji" column.
This script will:
1. Read the CSV file with separated date column
2. Extract all unique recruitment stages
3. Display them sorted alphabetically
4. Save them to a text file
"""

import csv
from collections import Counter

def extract_unique_stages(input_file, output_file=None):
    """
    Extract unique values from the "Etap w rekrutacji" column.
    
    Args:
        input_file (str): Path to input CSV file
        output_file (str): Optional path to output text file
    """
    
    unique_stages = set()
    stage_counts = Counter()
    total_rows = 0
    
    try:
        with open(input_file, 'r', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            
            for row in reader:
                stage = row['Etap w rekrutacji'].strip()
                if stage:  # Only add non-empty stages
                    unique_stages.add(stage)
                    stage_counts[stage] += 1
                total_rows += 1
        
        # Convert to sorted list
        sorted_stages = sorted(unique_stages)
        
        print(f"Total rows processed: {total_rows}")
        print(f"Number of unique recruitment stages: {len(sorted_stages)}")
        print("\n" + "="*60)
        print("UNIQUE RECRUITMENT STAGES (alphabetically sorted):")
        print("="*60)
        
        # Display unique stages with their counts
        for i, stage in enumerate(sorted_stages, 1):
            count = stage_counts[stage]
            print(f"{i:2d}. {stage} (appears {count} times)")
        
        # Save to file if output_file is specified
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write("UNIQUE RECRUITMENT STAGES\n")
                f.write("="*50 + "\n")
                f.write(f"Total rows processed: {total_rows}\n")
                f.write(f"Number of unique stages: {len(sorted_stages)}\n\n")
                
                f.write("Stages (alphabetically sorted):\n")
                f.write("-"*30 + "\n")
                for i, stage in enumerate(sorted_stages, 1):
                    count = stage_counts[stage]
                    f.write(f"{i:2d}. {stage} (appears {count} times)\n")
                
                f.write("\n" + "="*50 + "\n")
                f.write("Stages by frequency (most common first):\n")
                f.write("-"*30 + "\n")
                for i, (stage, count) in enumerate(stage_counts.most_common(), 1):
                    f.write(f"{i:2d}. {stage} ({count} times)\n")
            
            print(f"\nUnique stages saved to: {output_file}")
        
        # Also show top 10 most frequent stages
        print("\n" + "="*60)
        print("TOP 10 MOST FREQUENT STAGES:")
        print("="*60)
        for i, (stage, count) in enumerate(stage_counts.most_common(10), 1):
            percentage = (count / total_rows) * 100
            print(f"{i:2d}. {stage}")
            print(f"    Count: {count} ({percentage:.1f}% of all entries)")
        
        return sorted_stages, stage_counts
        
    except FileNotFoundError:
        print(f"Error: Input file '{input_file}' not found.")
        return None, None
    except Exception as e:
        print(f"Error processing file: {str(e)}")
        return None, None

def main():
    input_filename = "Kandydaci_with_separate_date.csv"
    output_filename = "unique_recruitment_stages.txt"
    
    print(f"Extracting unique stages from: {input_filename}")
    print("-" * 50)
    
    stages, counts = extract_unique_stages(input_filename, output_filename)
    
    if stages:
        print(f"\nProcess completed successfully!")
        print(f"Found {len(stages)} unique recruitment stages.")

if __name__ == "__main__":
    main()
