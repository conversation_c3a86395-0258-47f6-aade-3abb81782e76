#!/usr/bin/env python3
"""
Script to separate date from recruitment stage data in CSV file.
This script will:
1. Read the original CSV file
2. Split each row into Date and Recruitment Stage columns
3. Save the modified data to a new CSV file
"""

import csv
import re
from datetime import datetime

def separate_date_column(input_file, output_file):
    """
    Separate date from recruitment stage data and create proper CSV columns.
    
    Args:
        input_file (str): Path to input CSV file
        output_file (str): Path to output CSV file
    """
    
    # Pattern to match date at the beginning of each line (YYYY-MM-DD format)
    date_pattern = r'^(\d{4}-\d{2}-\d{2})\s+(.*)$'
    
    processed_rows = []
    
    try:
        with open(input_file, 'r', encoding='utf-8') as file:
            lines = file.readlines()
            
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            
            # Skip empty lines
            if not line:
                continue
                
            # Handle header row (first line)
            if line_num == 1 and line == "Etap w rekrutacji":
                processed_rows.append(["Date", "Etap w rekrutacji"])
                continue
            
            # Try to match date pattern
            match = re.match(date_pattern, line)
            
            if match:
                date_str = match.group(1)
                stage_str = match.group(2).strip()
                
                # Validate date format
                try:
                    datetime.strptime(date_str, '%Y-%m-%d')
                    processed_rows.append([date_str, stage_str])
                except ValueError:
                    print(f"Warning: Invalid date format on line {line_num}: {date_str}")
                    # If date is invalid, put entire line in stage column
                    processed_rows.append(["", line])
            else:
                # If no date pattern found, put entire line in stage column
                processed_rows.append(["", line])
                print(f"Warning: No date pattern found on line {line_num}: {line}")
        
        # Write to output CSV file
        with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerows(processed_rows)
            
        print(f"Successfully processed {len(processed_rows)} rows")
        print(f"Output saved to: {output_file}")
        
        # Display first few rows as preview
        print("\nPreview of processed data:")
        print("-" * 50)
        for i, row in enumerate(processed_rows[:10]):
            print(f"Row {i+1}: Date='{row[0]}', Stage='{row[1]}'")
            
    except FileNotFoundError:
        print(f"Error: Input file '{input_file}' not found.")
    except Exception as e:
        print(f"Error processing file: {str(e)}")

def main():
    input_filename = "Kandydaci -_ Teamtailor - Arkusz2.csv"
    output_filename = "Kandydaci_with_separate_date.csv"
    
    print(f"Processing file: {input_filename}")
    print(f"Output will be saved as: {output_filename}")
    print("-" * 50)
    
    separate_date_column(input_filename, output_filename)

if __name__ == "__main__":
    main()
